"""
Configuration management for the Doc2Dev system.

This module provides configuration loading and management functionality,
supporting both YAML files and environment variables.
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class VectorStoreConfig:
    """Vector store configuration"""
    provider: str = "oceanbase"
    connection_args: Dict[str, Any] = field(default_factory=lambda: {
        "host": "127.0.0.1",
        "port": 2881,
        "user": "root@test",
        "password": "admin",
        "db_name": "doc2dev",
        "charset": "utf8mb4"
    })
    table_name: str = "langchain_vector"


@dataclass
class EmbeddingConfig:
    """Embedding configuration"""
    provider: str = "dashscope"
    model: str = "text-embedding-v3"
    api_key: Optional[str] = None
    
    def __post_init__(self):
        # Auto-load API key from environment if not provided
        if self.api_key is None:
            if self.provider == "dashscope":
                self.api_key = os.getenv("DASHSCOPE_API_KEY")
            elif self.provider == "openai":
                self.api_key = os.getenv("OPENAI_API_KEY")


@dataclass
class MetadataDBConfig:
    """Metadata database configuration"""
    provider: str = "mysql"
    connection_args: Dict[str, Any] = field(default_factory=lambda: {
        "host": "127.0.0.1",
        "port": 2881,
        "user": "root@test",
        "password": "admin",
        "database": "doc2dev",
        "charset": "utf8mb4"
    })


@dataclass
class AppConfig:
    """Main application configuration"""
    vector_store: VectorStoreConfig = field(default_factory=VectorStoreConfig)
    embedding: EmbeddingConfig = field(default_factory=EmbeddingConfig)
    metadata_db: MetadataDBConfig = field(default_factory=MetadataDBConfig)
    
    # API settings
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_reload: bool = False
    
    # Logging settings
    log_level: str = "INFO"


class ConfigManager:
    """Configuration manager for loading and managing application settings"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self._config: Optional[AppConfig] = None
    
    def load_config(self) -> AppConfig:
        """Load configuration from file or create default configuration
        
        Returns:
            AppConfig: Application configuration
        """
        if self._config is not None:
            return self._config
        
        if self.config_file and os.path.exists(self.config_file):
            self._config = self._load_from_file(self.config_file)
        else:
            self._config = self._load_default_config()
        
        return self._config
    
    def _load_from_file(self, config_file: str) -> AppConfig:
        """Load configuration from YAML file
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            AppConfig: Loaded configuration
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Create configuration objects from loaded data
            vector_store_config = VectorStoreConfig(**config_data.get("vector_store", {}))
            embedding_config = EmbeddingConfig(**config_data.get("embedding", {}))
            metadata_db_config = MetadataDBConfig(**config_data.get("metadata_db", {}))
            
            # Extract API and logging settings
            api_settings = config_data.get("api", {})
            logging_settings = config_data.get("logging", {})
            
            return AppConfig(
                vector_store=vector_store_config,
                embedding=embedding_config,
                metadata_db=metadata_db_config,
                api_host=api_settings.get("host", "0.0.0.0"),
                api_port=api_settings.get("port", 8000),
                api_reload=api_settings.get("reload", False),
                log_level=logging_settings.get("level", "INFO")
            )
        except Exception as e:
            print(f"Error loading configuration from {config_file}: {e}")
            print("Using default configuration...")
            return self._load_default_config()
    
    def _load_default_config(self) -> AppConfig:
        """Load default configuration
        
        Returns:
            AppConfig: Default configuration
        """
        return AppConfig()


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """Get the current application configuration
    
    Returns:
        AppConfig: Current configuration
    """
    return config_manager.load_config()