"""
Pydantic models for API requests and responses.

This module defines the data models used in the FastAPI application
for request validation and response serialization.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, HttpUrl, Field


# Repository Models
class RepositoryBase(BaseModel):
    """Base repository model with common fields"""
    name: str = Field(..., description="Repository name")
    description: str = Field(default="", description="Repository description")
    repo: str = Field(..., description="Repository path (e.g., 'owner/repo')")
    repo_url: HttpUrl = Field(..., description="Repository URL")
    repo_status: str = Field(default="pending", description="Repository processing status")
    tokens: int = Field(default=0, description="Number of tokens in documents")
    snippets: int = Field(default=0, description="Number of code snippets")


class RepositoryCreate(RepositoryBase):
    """Model for creating a new repository"""
    pass


class RepositoryUpdate(BaseModel):
    """Model for updating a repository"""
    name: Optional[str] = None
    description: Optional[str] = None
    repo_status: Optional[str] = None
    tokens: Optional[int] = None
    snippets: Optional[int] = None


class RepositoryResponse(RepositoryBase):
    """Model for repository responses"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Document Models
class DocumentBase(BaseModel):
    """Base document model"""
    content: str = Field(..., description="Document content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")


class DocumentCreate(DocumentBase):
    """Model for creating a document"""
    pass


class DocumentResponse(DocumentBase):
    """Model for document responses"""
    id: Optional[str] = None
    similarity_score: Optional[float] = None

    class Config:
        from_attributes = True


# Search Models
class SearchRequest(BaseModel):
    """Model for search requests"""
    query: str = Field(..., description="Search query")
    k: int = Field(default=4, ge=1, le=50, description="Number of results to return")
    repository_filter: Optional[str] = Field(None, description="Filter by repository name")
    include_metadata: bool = Field(default=True, description="Include document metadata")
    include_scores: bool = Field(default=False, description="Include similarity scores")


class SearchResponse(BaseModel):
    """Model for search responses"""
    query: str
    results: List[DocumentResponse]
    total_results: int
    processing_time_ms: float


# Repository Processing Models
class RepositoryProcessRequest(BaseModel):
    """Model for repository processing requests"""
    repo_url: HttpUrl = Field(..., description="Repository URL to process")
    library_name: Optional[str] = Field(None, description="Custom library name")
    client_id: Optional[str] = Field(None, description="WebSocket client ID for progress updates")
    force_reprocess: bool = Field(default=False, description="Force reprocessing even if already exists")


class RepositoryProcessResponse(BaseModel):
    """Model for repository processing responses"""
    message: str
    repository_id: Optional[int] = None
    status: str
    processing_started: bool = False


# Progress Models
class ProgressUpdate(BaseModel):
    """Model for progress updates"""
    type: str = Field(..., description="Progress update type")
    status: str = Field(..., description="Current status")
    progress: float = Field(..., ge=0, le=100, description="Progress percentage")
    message: str = Field(..., description="Progress message")
    current_step: Optional[int] = None
    total_steps: Optional[int] = None
    details: Optional[Dict[str, Any]] = None


# Error Models
class ErrorResponse(BaseModel):
    """Model for error responses"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    error_code: Optional[str] = Field(None, description="Error code")


# Status Models
class HealthResponse(BaseModel):
    """Model for health check responses"""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(..., description="Response timestamp")
    components: Dict[str, str] = Field(..., description="Component status")


class StatsResponse(BaseModel):
    """Model for statistics responses"""
    total_repositories: int
    total_documents: int
    total_tokens: int
    total_snippets: int
    active_repositories: int
    failed_repositories: int


# Configuration Models
class ConfigResponse(BaseModel):
    """Model for configuration responses"""
    vector_store_provider: str
    embedding_provider: str
    metadata_db_provider: str
    api_version: str


# Embedding Models
class EmbedRequest(BaseModel):
    """Model for embedding requests"""
    texts: List[str] = Field(..., description="List of texts to embed")
    model: Optional[str] = Field(None, description="Embedding model to use")


class EmbedResponse(BaseModel):
    """Model for embedding responses"""
    embeddings: List[List[float]]
    model: str
    total_tokens: int
    processing_time_ms: float


# Vector Store Models
class VectorStoreInfo(BaseModel):
    """Model for vector store information"""
    table_name: str
    document_count: int
    provider: str
    status: str


class VectorStoreListResponse(BaseModel):
    """Model for vector store list responses"""
    vector_stores: List[VectorStoreInfo]
    total_count: int


# Batch Operation Models
class BatchDeleteRequest(BaseModel):
    """Model for batch delete requests"""
    repository_ids: List[int] = Field(..., description="List of repository IDs to delete")
    delete_vectors: bool = Field(default=True, description="Also delete associated vector data")


class BatchDeleteResponse(BaseModel):
    """Model for batch delete responses"""
    deleted_count: int
    failed_count: int
    errors: List[str] = Field(default_factory=list)


# WebSocket Models
class WebSocketMessage(BaseModel):
    """Model for WebSocket messages"""
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(default_factory=datetime.now)


# Pagination Models
class PaginationParams(BaseModel):
    """Model for pagination parameters"""
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")


class PaginatedResponse(BaseModel):
    """Base model for paginated responses"""
    page: int
    size: int
    total: int
    pages: int
    has_next: bool
    has_prev: bool


class PaginatedRepositoryResponse(PaginatedResponse):
    """Model for paginated repository responses"""
    items: List[RepositoryResponse]
