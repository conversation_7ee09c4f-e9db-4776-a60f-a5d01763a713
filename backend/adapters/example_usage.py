"""
Example usage of all adapters in the Doc2Dev system.

This module demonstrates how to use the various adapters:
- OceanBase vector store adapter
- MySQL/PostgreSQL metadata database adapters
- DashScope/OpenAI embedding adapters
"""

from typing import List, Dict, Any
from langchain_core.documents import Document

# Import adapters
from .vector_stores.oceanbase import OceanBaseVectorStoreAdapter
from .metadata.mysql import MySQLMetadataDB
from .metadata.postgresql import PostgreSQLMetadataDB
from .embeddings.dashscope import DashScopeEmbeddingAdapter
from .embeddings.openai import OpenAIEmbeddingAdapter

# Import core interfaces
from ..core.interfaces import Repository
from ..config.settings import get_config


def example_oceanbase_vector_store():
    """Example usage of OceanBase vector store adapter"""
    print("=== OceanBase Vector Store Adapter Example ===")
    
    # Get configuration
    config = get_config()
    
    # Create embedding adapter
    embedding_adapter = DashScopeEmbeddingAdapter(
        model="text-embedding-v3",
        api_key=config.embedding.api_key
    )
    
    # Create vector store adapter
    vector_adapter = OceanBaseVectorStoreAdapter(
        embedding_function=embedding_adapter.embeddings,
        connection_args=config.vector_store.connection_args,
        table_name="example_vectors"
    )
    
    # Example documents
    documents = [
        Document(
            page_content="This is a sample document about Python programming.",
            metadata={"source": "python_guide.md", "repository": "python-docs"}
        ),
        Document(
            page_content="FastAPI is a modern web framework for building APIs with Python.",
            metadata={"source": "fastapi_intro.md", "repository": "fastapi-docs"}
        )
    ]
    
    # Add documents
    print("Adding documents to vector store...")
    doc_ids = vector_adapter.add_documents(documents)
    print(f"Added {len(doc_ids)} documents")
    
    # Search for similar documents
    print("Searching for similar documents...")
    results = vector_adapter.similarity_search("Python web framework", k=2)
    for i, doc in enumerate(results):
        print(f"Result {i+1}: {doc.page_content[:50]}...")
    
    # Get table info
    table_info = vector_adapter.get_table_info()
    print(f"Table info: {table_info.get('row_count', 0)} documents")


def example_mysql_metadata_db():
    """Example usage of MySQL metadata database adapter"""
    print("\n=== MySQL Metadata Database Adapter Example ===")
    
    # Get configuration
    config = get_config()
    
    # Create MySQL adapter
    mysql_adapter = MySQLMetadataDB(config.metadata_db.connection_args)
    
    # Create a sample repository
    repository = Repository(
        name="example-repo",
        repo="owner/example-repo",
        repo_url="https://github.com/owner/example-repo",
        description="An example repository for testing",
        repo_status="completed",
        tokens=1500,
        snippets=25
    )
    
    # Add repository
    print("Adding repository...")
    success = mysql_adapter.add_repository(repository)
    print(f"Repository added: {success}")
    
    # Get all repositories
    print("Getting all repositories...")
    repositories = mysql_adapter.get_repositories()
    print(f"Found {len(repositories)} repositories")
    
    # Get repository by name
    found_repo = mysql_adapter.get_repository_by_name("example-repo")
    if found_repo:
        print(f"Found repository: {found_repo['name']}")
        
        # Update repository
        repository.id = found_repo['id']
        repository.tokens = 2000
        repository.snippets = 30
        
        print("Updating repository...")
        success = mysql_adapter.update_repository(repository)
        print(f"Repository updated: {success}")


def example_dashscope_embedding():
    """Example usage of DashScope embedding adapter"""
    print("\n=== DashScope Embedding Adapter Example ===")
    
    # Create DashScope adapter
    dashscope_adapter = DashScopeEmbeddingAdapter(
        model="text-embedding-v3"
    )
    
    # Get model info
    model_info = dashscope_adapter.get_model_info()
    print(f"Model: {model_info['model']}")
    print(f"Embedding dimension: {model_info['embedding_dimension']}")
    print(f"API key valid: {model_info['api_key_valid']}")
    
    # Embed some texts
    texts = [
        "Python is a programming language",
        "FastAPI is a web framework",
        "Machine learning with Python"
    ]
    
    print("Embedding texts...")
    embeddings = dashscope_adapter.embed_documents(texts)
    print(f"Generated {len(embeddings)} embeddings")
    
    # Estimate cost
    cost_info = dashscope_adapter.estimate_cost(texts)
    print(f"Estimated cost: ¥{cost_info['estimated_cost_rmb']:.6f}")


def example_openai_embedding():
    """Example usage of OpenAI embedding adapter"""
    print("\n=== OpenAI Embedding Adapter Example ===")
    
    try:
        # Create OpenAI adapter
        openai_adapter = OpenAIEmbeddingAdapter(
            model="text-embedding-3-small"
        )
        
        # Get model info
        model_info = openai_adapter.get_model_info()
        print(f"Model: {model_info['model']}")
        print(f"Embedding dimension: {model_info['embedding_dimension']}")
        print(f"API key valid: {model_info['api_key_valid']}")
        
        # Embed a query
        query = "How to use FastAPI?"
        print("Embedding query...")
        query_embedding = openai_adapter.embed_query(query)
        print(f"Query embedding dimension: {len(query_embedding)}")
        
    except ValueError as e:
        print(f"OpenAI adapter not available: {e}")


def example_postgresql_metadata_db():
    """Example usage of PostgreSQL metadata database adapter"""
    print("\n=== PostgreSQL Metadata Database Adapter Example ===")
    
    try:
        # PostgreSQL connection args (example)
        pg_connection_args = {
            "host": "localhost",
            "port": 5432,
            "user": "postgres",
            "password": "password",
            "database": "doc2dev"
        }
        
        # Create PostgreSQL adapter
        pg_adapter = PostgreSQLMetadataDB(pg_connection_args)
        
        # Test connection
        print("Testing PostgreSQL connection...")
        repositories = pg_adapter.get_repositories()
        print(f"PostgreSQL adapter working, found {len(repositories)} repositories")
        
    except Exception as e:
        print(f"PostgreSQL adapter not available: {e}")


def example_integrated_workflow():
    """Example of integrated workflow using multiple adapters"""
    print("\n=== Integrated Workflow Example ===")
    
    # Get configuration
    config = get_config()
    
    # 1. Create embedding adapter
    embedding_adapter = DashScopeEmbeddingAdapter(
        model=config.embedding.model,
        api_key=config.embedding.api_key
    )
    
    # 2. Create metadata database adapter
    metadata_adapter = MySQLMetadataDB(config.metadata_db.connection_args)
    
    # 3. Create vector store adapter
    vector_adapter = OceanBaseVectorStoreAdapter(
        embedding_function=embedding_adapter.embeddings,
        connection_args=config.vector_store.connection_args,
        table_name="integrated_example"
    )
    
    # 4. Add a repository to metadata
    repository = Repository(
        name="integrated-example",
        repo="owner/integrated-example",
        repo_url="https://github.com/owner/integrated-example",
        description="Integrated workflow example",
        repo_status="in_progress"
    )
    
    print("Adding repository to metadata database...")
    metadata_adapter.add_repository(repository)
    
    # 5. Add documents to vector store
    documents = [
        Document(
            page_content="This document explains the integrated workflow.",
            metadata={"source": "workflow.md", "repository": "integrated-example"}
        )
    ]
    
    print("Adding documents to vector store...")
    vector_adapter.add_documents(documents)
    
    # 6. Update repository status
    repo_data = metadata_adapter.get_repository_by_name("integrated-example")
    if repo_data:
        repository.id = repo_data['id']
        repository.repo_status = "completed"
        repository.tokens = 100
        repository.snippets = 5
        
        print("Updating repository status...")
        metadata_adapter.update_repository(repository)
    
    print("Integrated workflow completed successfully!")


if __name__ == "__main__":
    """Run all examples"""
    try:
        example_dashscope_embedding()
        example_openai_embedding()
        example_mysql_metadata_db()
        example_postgresql_metadata_db()
        example_oceanbase_vector_store()
        example_integrated_workflow()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have proper configuration and API keys set up.")
