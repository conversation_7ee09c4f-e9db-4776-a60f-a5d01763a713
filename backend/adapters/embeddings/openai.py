"""
OpenAI embedding adapter.

This module provides an enhanced adapter for OpenAI embedding operations,
wrapping LangChain's OpenAIEmbeddings with additional functionality.
"""

import os
from typing import List, Dict, Any, Optional
from langchain_core.embeddings import Embeddings
from langchain_openai import OpenAIEmbeddings


class OpenAIEmbeddingAdapter:
    """Enhanced adapter for OpenAI embedding operations"""
    
    def __init__(self, model: str = "text-embedding-3-small", api_key: Optional[str] = None, **kwargs):
        """Initialize the OpenAI embedding adapter
        
        Args:
            model: OpenAI embedding model name
            api_key: OpenAI API key (if None, will try to get from environment)
            **kwargs: Additional keyword arguments for OpenAIEmbeddings
        """
        self.model = model
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.kwargs = kwargs
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or provide api_key parameter.")
        
        self._embeddings: Optional[OpenAIEmbeddings] = None
    
    @property
    def embeddings(self) -> OpenAIEmbeddings:
        """Get or create the OpenAI embeddings instance"""
        if self._embeddings is None:
            self._embeddings = OpenAIEmbeddings(
                model=self.model,
                openai_api_key=self.api_key,
                **self.kwargs
            )
        return self._embeddings
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents
        
        Args:
            texts: List of text documents to embed
            
        Returns:
            List of embedding vectors
        """
        return self.embeddings.embed_documents(texts)
    
    def embed_query(self, text: str) -> List[float]:
        """Embed a single query text
        
        Args:
            text: Query text to embed
            
        Returns:
            Embedding vector
        """
        return self.embeddings.embed_query(text)
    
    def embed_documents_with_metadata(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Embed documents and preserve their metadata
        
        Args:
            documents: List of documents with 'text' and optional 'metadata' fields
            
        Returns:
            List of documents with added 'embedding' field
        """
        texts = [doc.get('text', '') for doc in documents]
        embeddings = self.embed_documents(texts)
        
        result = []
        for doc, embedding in zip(documents, embeddings):
            result_doc = doc.copy()
            result_doc['embedding'] = embedding
            result.append(result_doc)
        
        return result
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of the embedding vectors
        
        Returns:
            Dimension of embedding vectors
        """
        # Different OpenAI models have different dimensions
        model_dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536,
        }
        
        if self.model in model_dimensions:
            return model_dimensions[self.model]
        else:
            # Test with a simple text to get dimension
            test_embedding = self.embed_query("test")
            return len(test_embedding)
    
    def batch_embed_with_progress(self, texts: List[str], batch_size: int = 100, progress_callback=None) -> List[List[float]]:
        """Embed documents in batches with progress tracking
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch
            progress_callback: Optional callback function to report progress
            
        Returns:
            List of embedding vectors
        """
        all_embeddings = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_embeddings = self.embed_documents(batch_texts)
            all_embeddings.extend(batch_embeddings)
            
            if progress_callback:
                current_batch = (i // batch_size) + 1
                progress = (current_batch / total_batches) * 100
                progress_callback(progress, current_batch, total_batches)
        
        return all_embeddings
    
    def validate_api_key(self) -> bool:
        """Validate the OpenAI API key by making a test request
        
        Returns:
            bool: Whether the API key is valid
        """
        try:
            # Try to embed a simple test text
            self.embed_query("test")
            return True
        except Exception as e:
            print(f"API key validation failed: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current embedding model
        
        Returns:
            Dictionary containing model information
        """
        return {
            "model": self.model,
            "api_key_set": bool(self.api_key),
            "api_key_valid": self.validate_api_key(),
            "embedding_dimension": self.get_embedding_dimension(),
            "provider": "openai"
        }
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate the number of tokens in a text
        
        Args:
            text: Input text
            
        Returns:
            Estimated number of tokens
        """
        # Simple estimation: roughly 1 token per 4 characters for English text
        # This is a rough approximation and may not be accurate for all cases
        return len(text) // 4
    
    def estimate_cost(self, texts: List[str]) -> Dict[str, Any]:
        """Estimate the cost of embedding a list of texts
        
        Args:
            texts: List of texts to estimate cost for
            
        Returns:
            Dictionary containing cost estimation
        """
        total_tokens = sum(self.estimate_tokens(text) for text in texts)
        
        # OpenAI pricing (as of 2024, may change)
        model_pricing = {
            "text-embedding-3-small": 0.00002,  # $0.00002 per 1K tokens
            "text-embedding-3-large": 0.00013,  # $0.00013 per 1K tokens
            "text-embedding-ada-002": 0.0001,   # $0.0001 per 1K tokens
        }
        
        cost_per_1k_tokens = model_pricing.get(self.model, 0.0001)  # Default to ada-002 pricing
        estimated_cost = (total_tokens / 1000) * cost_per_1k_tokens
        
        return {
            "total_texts": len(texts),
            "estimated_tokens": total_tokens,
            "estimated_cost_usd": estimated_cost,
            "cost_per_1k_tokens": cost_per_1k_tokens,
            "model": self.model
        }


class OpenAIEmbeddingFactory:
    """Factory for creating OpenAI embedding adapters"""
    
    @staticmethod
    def create(config: Dict[str, Any]) -> OpenAIEmbeddingAdapter:
        """Create an OpenAI embedding adapter from configuration
        
        Args:
            config: Configuration dictionary
            
        Returns:
            OpenAIEmbeddingAdapter instance
        """
        return OpenAIEmbeddingAdapter(
            model=config.get("model", "text-embedding-3-small"),
            api_key=config.get("api_key"),
            **{k: v for k, v in config.items() if k not in ["model", "api_key"]}
        )
    
    @staticmethod
    def create_from_env(model: str = "text-embedding-3-small") -> OpenAIEmbeddingAdapter:
        """Create an OpenAI embedding adapter using environment variables
        
        Args:
            model: OpenAI embedding model name
            
        Returns:
            OpenAIEmbeddingAdapter instance
        """
        return OpenAIEmbeddingAdapter(model=model)
