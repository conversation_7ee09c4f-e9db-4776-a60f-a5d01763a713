"""
OceanBase vector store adapter.

This module provides an enhanced adapter for OceanBase vector store operations,
wrapping LangChain's OceanbaseVectorStore with additional functionality.
"""

from typing import List, Dict, Any, Optional
from langchain_core.vectorstores import VectorStore
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document
from langchain_oceanbase.vectorstores import OceanbaseVectorStore
import pymysql


class OceanBaseVectorStoreAdapter:
    """Enhanced adapter for OceanBase vector store operations"""
    
    def __init__(self, embedding_function: Embeddings, connection_args: Dict[str, Any], table_name: str = "langchain_vector"):
        """Initialize the OceanBase vector store adapter
        
        Args:
            embedding_function: Embedding function to use
            connection_args: OceanBase connection arguments
            table_name: Name of the vector table
        """
        self.embedding_function = embedding_function
        self.connection_args = connection_args
        self.table_name = self._sanitize_table_name(table_name)
        self._vector_store: Optional[OceanbaseVectorStore] = None
    
    def _sanitize_table_name(self, table_name: str) -> str:
        """Sanitize table name by replacing invalid characters
        
        Args:
            table_name: Original table name
            
        Returns:
            Sanitized table name
        """
        # Replace hyphens and other invalid characters with underscores
        return table_name.replace('-', '_').replace('.', '_').replace('/', '_')
    
    @property
    def vector_store(self) -> OceanbaseVectorStore:
        """Get or create the vector store instance"""
        if self._vector_store is None:
            self._vector_store = OceanbaseVectorStore(
                embedding_function=self.embedding_function,
                table_name=self.table_name,
                connection_args=self.connection_args,
                vidx_metric_type="l2"
            )
        return self._vector_store
    
    def add_documents(self, documents: List[Document], **kwargs: Any) -> List[str]:
        """Add documents to the vector store
        
        Args:
            documents: List of documents to add
            **kwargs: Additional keyword arguments
            
        Returns:
            List of document IDs
        """
        return self.vector_store.add_documents(documents, **kwargs)
    
    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs: Any) -> List[Document]:
        """Search for documents similar to the query
        
        Args:
            query: Query string
            k: Number of documents to return
            filter: Optional filter for document metadata
            **kwargs: Additional keyword arguments
            
        Returns:
            List of similar documents
        """
        return self.vector_store.similarity_search(
            query=query,
            k=k,
            filter=filter,
            **kwargs
        )
    
    def similarity_search_with_score(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs: Any) -> List[tuple]:
        """Search for documents similar to the query with similarity scores
        
        Args:
            query: Query string
            k: Number of documents to return
            filter: Optional filter for document metadata
            **kwargs: Additional keyword arguments
            
        Returns:
            List of (document, score) tuples
        """
        return self.vector_store.similarity_search_with_score(
            query=query,
            k=k,
            filter=filter,
            **kwargs
        )
    
    def delete_documents(self, ids: List[str]) -> bool:
        """Delete documents from the vector store
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            bool: Whether deletion was successful
        """
        try:
            self.vector_store.delete(ids)
            return True
        except Exception as e:
            print(f"Error deleting documents: {e}")
            return False
    
    def get_table_info(self) -> Dict[str, Any]:
        """Get information about the vector table
        
        Returns:
            Dictionary containing table information
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get table information
                cursor.execute(f"SHOW TABLE STATUS LIKE '{self.table_name}'")
                table_status = cursor.fetchone()
                
                # Get column information
                cursor.execute(f"DESCRIBE {self.table_name}")
                columns = cursor.fetchall()
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) as count FROM {self.table_name}")
                row_count = cursor.fetchone()['count']
                
                return {
                    "table_name": self.table_name,
                    "row_count": row_count,
                    "table_status": table_status,
                    "columns": columns
                }
        except Exception as e:
            print(f"Error getting table info: {e}")
            return {}
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def table_exists(self) -> bool:
        """Check if the vector table exists
        
        Returns:
            bool: Whether the table exists
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            with connection.cursor() as cursor:
                cursor.execute(f"SHOW TABLES LIKE '{self.table_name}'")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            print(f"Error checking table existence: {e}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def drop_table(self) -> bool:
        """Drop the vector table
        
        Returns:
            bool: Whether the table was dropped successfully
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            with connection.cursor() as cursor:
                cursor.execute(f"DROP TABLE IF EXISTS {self.table_name}")
                connection.commit()
                return True
        except Exception as e:
            print(f"Error dropping table: {e}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    @classmethod
    def from_documents(
        cls,
        documents: List[Document],
        embedding_function: Embeddings,
        connection_args: Dict[str, Any],
        table_name: str = "langchain_vector",
        drop_old: bool = False,
        **kwargs: Any
    ) -> "OceanBaseVectorStoreAdapter":
        """Create a vector store adapter from documents
        
        Args:
            documents: List of documents to add
            embedding_function: Embedding function to use
            connection_args: OceanBase connection arguments
            table_name: Name of the vector table
            drop_old: Whether to drop existing table
            **kwargs: Additional keyword arguments
            
        Returns:
            OceanBaseVectorStoreAdapter instance
        """
        adapter = cls(embedding_function, connection_args, table_name)
        
        # Drop table if requested
        if drop_old:
            adapter.drop_table()
        
        # Create vector store from documents
        adapter._vector_store = OceanbaseVectorStore.from_documents(
            documents=documents,
            embedding=embedding_function,
            connection_args=connection_args,
            table_name=adapter.table_name,
            vidx_metric_type="l2",
            drop_old=drop_old,
            **kwargs
        )
        
        return adapter
    
    def get_document_count(self) -> int:
        """Get the number of documents in the vector store
        
        Returns:
            Number of documents
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                result = cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            print(f"Error getting document count: {e}")
            return 0
        finally:
            if 'connection' in locals() and connection:
                connection.close()
