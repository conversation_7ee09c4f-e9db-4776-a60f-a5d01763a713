"""
MySQL metadata database adapter.

This module provides a MySQL implementation of the MetadataDBInterface
for repository metadata operations.
"""

import pymysql
from typing import List, Dict, Any, Optional
from pymysql.cursors import DictCursor

from ...core.interfaces import MetadataDBInterface, Repository


class MySQLMetadataDB(MetadataDBInterface):
    """MySQL implementation of metadata database operations"""
    
    def __init__(self, connection_args: Dict[str, Any]):
        """Initialize MySQL metadata database adapter
        
        Args:
            connection_args: MySQL connection arguments
        """
        self.connection_args = connection_args
    
    def get_db_connection(self):
        """Get database connection
        
        Returns:
            Database connection object
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            return connection
        except Exception as e:
            print(f"Database connection failed: {str(e)}")
            raise
    
    def get_repositories(self, field: Optional[str] = None, value: Optional[Any] = None) -> List[Dict[str, Any]]:
        """Get repositories, with optional filtering
        
        Args:
            field: Optional database field to filter on ('name', 'repo', or 'id')
                  If None, returns all repositories
            value: Value to filter by, required if field is specified
            
        Returns:
            List[Dict]: List of repository information dictionaries
                       Returns a single-item list if field is specified and found
                       Returns empty list if no matches or on error
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor(DictCursor) as cursor:
                # Base SQL query
                sql = """
                SELECT id, name, description, repo, repo_url, 
                       tokens, snippets, repo_status, created_at, updated_at
                FROM repositories
                """
                
                # Determine if we're getting all repositories or filtering
                if field is not None:
                    # Validate field
                    valid_fields = ['name', 'repo', 'id']
                    if field not in valid_fields:
                        raise ValueError(f"Invalid field: {field}. Must be one of {valid_fields}")
                    
                    # Add WHERE clause for filtering
                    sql += f"WHERE {field} = %s"
                    cursor.execute(sql, (value,))
                    
                    # For single item queries, wrap result in a list for consistent return type
                    result = cursor.fetchone()
                    return [result] if result else []
                else:
                    # Get all repositories
                    sql += "ORDER BY name"
                    cursor.execute(sql)
                    return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get repositories: {str(e)}")
            return []
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def add_repository(self, repository: Repository) -> bool:
        """Add a new repository using a Repository object
        
        Args:
            repository: Repository object containing repository information
            
        Returns:
            bool: Whether the addition was successful
        """
        try:
            # Verify required fields
            if not repository.name or not repository.repo or not repository.repo_url:
                raise ValueError("Missing required fields: name, repo, or repo_url")
            
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = """
                INSERT INTO repositories (name, description, repo, repo_url, repo_status, tokens, snippets)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(sql, (
                    repository.name,
                    repository.description,
                    repository.repo,
                    repository.repo_url,
                    repository.repo_status,
                    repository.tokens,
                    repository.snippets
                ))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to add repository: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def update_repository(self, repository: Repository) -> bool:
        """Update repository using a Repository object
        
        Args:
            repository: Repository object containing updated information
            
        Returns:
            bool: Whether the update was successful
        """
        try:
            # Ensure repository has an ID
            if repository.id is None:
                raise ValueError("Repository ID is required for update")
            
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = """
                UPDATE repositories
                SET name = %s, description = %s, repo = %s, repo_url = %s, 
                    repo_status = %s, tokens = %s, snippets = %s
                WHERE id = %s
                """
                
                cursor.execute(sql, (
                    repository.name,
                    repository.description,
                    repository.repo,
                    repository.repo_url,
                    repository.repo_status,
                    repository.tokens,
                    repository.snippets,
                    repository.id
                ))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to update repository: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def delete_repository(self, repository_id: int) -> bool:
        """Delete a repository
        
        Args:
            repository_id: ID of the repository to delete
            
        Returns:
            bool: Whether the deletion was successful
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = "DELETE FROM repositories WHERE id = %s"
                cursor.execute(sql, (repository_id,))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to delete repository: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def get_repository_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get repository by name
        
        Args:
            name: Repository name
            
        Returns:
            Repository dictionary or None if not found
        """
        results = self.get_repositories(field="name", value=name)
        return results[0] if results else None
    
    def get_repository_by_id(self, repo_id: int) -> Optional[Dict[str, Any]]:
        """Get repository by ID
        
        Args:
            repo_id: Repository ID
            
        Returns:
            Repository dictionary or None if not found
        """
        results = self.get_repositories(field="id", value=repo_id)
        return results[0] if results else None
    
    def get_repository_by_path(self, repo_path: str) -> Optional[Dict[str, Any]]:
        """Get repository by path
        
        Args:
            repo_path: Repository path (e.g., "owner/repo")
            
        Returns:
            Repository dictionary or None if not found
        """
        results = self.get_repositories(field="repo", value=repo_path)
        return results[0] if results else None
    
    def update_repository_status(self, repo_id: int, status: str) -> bool:
        """Update repository status
        
        Args:
            repo_id: Repository ID
            status: New status
            
        Returns:
            bool: Whether the update was successful
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = "UPDATE repositories SET repo_status = %s WHERE id = %s"
                cursor.execute(sql, (status, repo_id))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to update repository status: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
    
    def update_repository_counts(self, repo_id: int, tokens: int, snippets: int) -> bool:
        """Update repository token and snippet counts
        
        Args:
            repo_id: Repository ID
            tokens: Number of tokens
            snippets: Number of snippets
            
        Returns:
            bool: Whether the update was successful
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = "UPDATE repositories SET tokens = %s, snippets = %s WHERE id = %s"
                cursor.execute(sql, (tokens, snippets, repo_id))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to update repository counts: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()
