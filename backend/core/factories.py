"""
Factory classes for creating component instances based on configuration.

This module provides factory classes for:
- VectorStoreFactory: Creates vector store instances
- EmbeddingFactory: Creates embedding model instances  
- MetadataDBFactory: Creates metadata database instances
"""

from typing import Dict, Any
from langchain_core.vectorstores import VectorStore
from langchain_core.embeddings import Embeddings
from langchain_oceanbase.vectorstores import OceanbaseVectorStore
from langchain_community.embeddings import DashScopeEmbeddings, OpenAIEmbeddings

from .interfaces import MetadataDBInterface


class VectorStoreFactory:
    """Factory for creating vector store instances"""
    
    @staticmethod
    def create(embedding_function: Embeddings, config: Dict[str, Any]) -> VectorStore:
        """Create a vector store instance based on configuration
        
        Args:
            embedding_function: Embedding function to use
            config: Vector store configuration
            
        Returns:
            VectorStore instance
        """
        provider = config["provider"]
        
        if provider == "oceanbase":
            return OceanbaseVectorStore(
                embedding_function=embedding_function,
                table_name=config["table_name"],
                connection_args=config["connection_args"],
                vidx_metric_type="l2"
            )
        # Add more providers as needed
        # elif provider == "chroma":
        #     from langchain_community.vectorstores import Chroma
        #     return Chroma(
        #         embedding_function=embedding_function,
        #         persist_directory=config.get("persist_directory")
        #     )
        # elif provider == "pgvector":
        #     from langchain_community.vectorstores import PGVector
        #     return PGVector(
        #         embedding_function=embedding_function,
        #         connection_string=config["connection_string"],
        #         collection_name=config["collection_name"]
        #     )
        else:
            raise ValueError(f"Unsupported vector store provider: {provider}")
    
    @staticmethod
    def delete(table_name: str, connection_args: Dict[str, Any], provider: str = "oceanbase") -> bool:
        """Delete a vector table
         
        Args:
            table_name: Name of the vector table to delete
            connection_args: Connection arguments for the database
            provider: Vector store provider (default: "oceanbase")
             
        Returns:
            bool: Whether the deletion was successful
        """
        try:
            if provider == "oceanbase":
                # Connect to OceanBase
                import pymysql
                connection = pymysql.connect(**connection_args)
                
                with connection.cursor() as cursor:
                    # Drop the vector table
                    sql = f"DROP TABLE IF EXISTS {table_name}"
                    cursor.execute(sql)
                    connection.commit()
                return True
            # Add more providers as needed
            else:
                raise ValueError(f"Unsupported vector store provider for deletion: {provider}")
        except Exception as e:
            print(f"Failed to delete vector table: {str(e)}")
            return False
        finally:
            if 'connection' in locals() and connection:
                connection.close()


class EmbeddingFactory:
    """Factory for creating embedding model instances"""
    
    @staticmethod
    def create(config: Dict[str, Any]) -> Embeddings:
        """Create an embedding model instance based on configuration
        
        Args:
            config: Embedding configuration
            
        Returns:
            Embeddings instance
        """
        provider = config["provider"]
        
        if provider == "dashscope":
            return DashScopeEmbeddings(
                model=config["model"],
                dashscope_api_key=config.get("api_key")
            )
        elif provider == "openai":
            return OpenAIEmbeddings(
                model=config["model"],
                openai_api_key=config.get("api_key")
            )
        # Add more providers as needed
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")


class MetadataDBFactory:
    """Factory for creating metadata database instances"""
    
    @staticmethod
    def create(config: Dict[str, Any]) -> MetadataDBInterface:
        """Create a metadata database instance based on configuration
        
        Args:
            config: Metadata database configuration
            
        Returns:
            MetadataDBInterface instance
        """
        provider = config["provider"]
        
        if provider == "mysql":
            # Import here to avoid circular imports
            from ..adapters.metadata.mysql import MySQLMetadataDB
            return MySQLMetadataDB(config["connection_args"])
        elif provider == "postgresql":
            # Import here to avoid circular imports  
            from ..adapters.metadata.postgresql import PostgreSQLMetadataDB
            return PostgreSQLMetadataDB(config["connection_args"])
        # Add more providers as needed
        else:
            raise ValueError(f"Unsupported metadata database provider: {provider}")
