"""
Core interfaces for the Doc2Dev system.

This module defines abstract interfaces for the main components:
- MetadataDBInterface: For repository metadata operations
- Repository: Data model for repository information
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Repository:
    """Repository data model"""
    name: str
    repo: str
    repo_url: str
    description: str = ''
    repo_status: str = 'pending'
    tokens: int = 0
    snippets: int = 0
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class MetadataDBInterface(ABC):
    """Abstract interface for metadata database operations"""
    
    @abstractmethod
    def get_db_connection(self):
        """Get database connection
        
        Returns:
            Database connection object
        """
        pass
    
    @abstractmethod
    def get_repositories(self, field: Optional[str] = None, value: Optional[Any] = None) -> List[Dict[str, Any]]:
        """Get repositories, with optional filtering
        
        Args:
            field: Optional database field to filter on ('name', 'repo', or 'id')
                  If None, returns all repositories
            value: Value to filter by, required if field is specified
            
        Returns:
            List[Dict]: List of repository information dictionaries
                       Returns a single-item list if field is specified and found
                       Returns empty list if no matches or on error
        """
        pass
    
    @abstractmethod
    def add_repository(self, repository: Repository) -> bool:
        """Add a new repository using a Repository object
        
        Args:
            repository: Repository object containing repository information
            
        Returns:
            bool: Whether the addition was successful
        """
        pass
    
    @abstractmethod
    def update_repository(self, repository: Repository) -> bool:
        """Update repository using a Repository object
        
        Args:
            repository: Repository object containing updated information
            
        Returns:
            bool: Whether the update was successful
        """
        pass
    
    @abstractmethod
    def delete_repository(self, repository_id: int) -> bool:
        """Delete a repository
        
        Args:
            repository_id: ID of the repository to delete
            
        Returns:
            bool: Whether the deletion was successful
        """
        pass


# Note: For Vector Stores and Embeddings, we will use LangChain's existing interfaces:
# - langchain_core.vectorstores.VectorStore for vector store operations
# - langchain_core.embeddings.Embeddings for embedding operations
# These are well-established interfaces that provide all the functionality we need.
