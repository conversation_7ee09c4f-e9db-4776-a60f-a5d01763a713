"""
Core business logic services for the Doc2Dev system.

This module provides high-level service classes that coordinate
between different components (vector stores, embeddings, metadata DB).
"""

from typing import List, Dict, Any, Optional
from langchain_core.vectorstores import VectorStore
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

from .interfaces import MetadataDBInterface, Repository
from .factories import VectorStoreFactory, EmbeddingFactory, MetadataDBFactory
from ..config.settings import AppConfig


class VectorService:
    """Service for vector store operations"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
    
    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs: Any) -> List[Document]:
        """Search for documents similar to the query
        
        Args:
            query: Query string
            k: Number of documents to return
            filter: Optional filter for document metadata
            **kwargs: Additional keyword arguments to pass to the vector store
            
        Returns:
            List of similar documents
        """
        return self.vector_store.similarity_search(
            query=query,
            k=k,
            filter=filter,
            **kwargs
        )
    
    def add_documents(self, documents: List[Document], **kwargs: Any) -> List[str]:
        """Add documents to the vector store
        
        Args:
            documents: List of documents to add
            **kwargs: Additional keyword arguments to pass to the vector store
            
        Returns:
            List of document IDs
        """
        return self.vector_store.add_documents(documents, **kwargs)
    
    def delete_documents(self, ids: List[str]) -> bool:
        """Delete documents from the vector store
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            bool: Whether deletion was successful
        """
        try:
            self.vector_store.delete(ids)
            return True
        except Exception as e:
            print(f"Error deleting documents: {e}")
            return False


class RepositoryService:
    """Service for repository metadata operations"""
    
    def __init__(self, metadata_db: MetadataDBInterface):
        self.metadata_db = metadata_db
    
    def get_all_repositories(self) -> List[Dict[str, Any]]:
        """Get all repositories
        
        Returns:
            List of repository dictionaries
        """
        return self.metadata_db.get_repositories()
    
    def get_repository_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get repository by name
        
        Args:
            name: Repository name
            
        Returns:
            Repository dictionary or None if not found
        """
        results = self.metadata_db.get_repositories(field="name", value=name)
        return results[0] if results else None
    
    def get_repository_by_id(self, repo_id: int) -> Optional[Dict[str, Any]]:
        """Get repository by ID
        
        Args:
            repo_id: Repository ID
            
        Returns:
            Repository dictionary or None if not found
        """
        results = self.metadata_db.get_repositories(field="id", value=repo_id)
        return results[0] if results else None
    
    def add_repository(self, repository: Repository) -> bool:
        """Add a new repository
        
        Args:
            repository: Repository object
            
        Returns:
            bool: Whether addition was successful
        """
        return self.metadata_db.add_repository(repository)
    
    def update_repository(self, repository: Repository) -> bool:
        """Update an existing repository
        
        Args:
            repository: Repository object with updated information
            
        Returns:
            bool: Whether update was successful
        """
        return self.metadata_db.update_repository(repository)
    
    def delete_repository(self, repo_id: int) -> bool:
        """Delete a repository
        
        Args:
            repo_id: Repository ID
            
        Returns:
            bool: Whether deletion was successful
        """
        return self.metadata_db.delete_repository(repo_id)


class Doc2DevService:
    """Main service that coordinates all components"""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self._embedding_model: Optional[Embeddings] = None
        self._vector_store: Optional[VectorStore] = None
        self._metadata_db: Optional[MetadataDBInterface] = None
        self._vector_service: Optional[VectorService] = None
        self._repository_service: Optional[RepositoryService] = None
    
    @property
    def embedding_model(self) -> Embeddings:
        """Get or create embedding model"""
        if self._embedding_model is None:
            self._embedding_model = EmbeddingFactory.create(self.config.embedding.__dict__)
        return self._embedding_model
    
    @property
    def vector_store(self) -> VectorStore:
        """Get or create vector store"""
        if self._vector_store is None:
            self._vector_store = VectorStoreFactory.create(
                self.embedding_model, 
                self.config.vector_store.__dict__
            )
        return self._vector_store
    
    @property
    def metadata_db(self) -> MetadataDBInterface:
        """Get or create metadata database"""
        if self._metadata_db is None:
            self._metadata_db = MetadataDBFactory.create(self.config.metadata_db.__dict__)
        return self._metadata_db
    
    @property
    def vector_service(self) -> VectorService:
        """Get or create vector service"""
        if self._vector_service is None:
            self._vector_service = VectorService(self.vector_store)
        return self._vector_service
    
    @property
    def repository_service(self) -> RepositoryService:
        """Get or create repository service"""
        if self._repository_service is None:
            self._repository_service = RepositoryService(self.metadata_db)
        return self._repository_service
    
    def search_documents(self, query: str, repository_name: Optional[str] = None, k: int = 4) -> List[Document]:
        """Search for documents with optional repository filtering
        
        Args:
            query: Search query
            repository_name: Optional repository name to filter by
            k: Number of results to return
            
        Returns:
            List of matching documents
        """
        filter_dict = None
        if repository_name:
            filter_dict = {"repository": repository_name}
        
        return self.vector_service.similarity_search(query, k=k, filter=filter_dict)
    
    def add_repository_documents(self, repository_name: str, documents: List[Document]) -> bool:
        """Add documents for a specific repository
        
        Args:
            repository_name: Name of the repository
            documents: List of documents to add
            
        Returns:
            bool: Whether addition was successful
        """
        # Add repository metadata to documents
        for doc in documents:
            doc.metadata["repository"] = repository_name
        
        try:
            self.vector_service.add_documents(documents)
            return True
        except Exception as e:
            print(f"Error adding documents for repository {repository_name}: {e}")
            return False
