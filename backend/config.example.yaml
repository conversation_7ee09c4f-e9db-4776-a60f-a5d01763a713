# Doc2Dev Configuration Example
# Copy this file to config.yaml and modify as needed

vector_store:
  provider: oceanbase
  connection_args:
    host: 127.0.0.1
    port: 2881
    user: root@test
    password: admin
    db_name: doc2dev
    charset: utf8mb4
  table_name: langchain_vector

embedding:
  provider: dashscope
  model: text-embedding-v3
  api_key: your-dashscope-api-key  # Or set DASHSCOPE_API_KEY environment variable

metadata_db:
  provider: mysql
  connection_args:
    host: 127.0.0.1
    port: 2881
    user: root@test
    password: admin
    database: doc2dev
    charset: utf8mb4

api:
  host: 0.0.0.0
  port: 8000
  reload: false

logging:
  level: INFO
