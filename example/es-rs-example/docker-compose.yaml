services:
  elasticsearch:
    container_name: es-single-node
    image: docker.elastic.co/elasticsearch/elasticsearch:9.0.0
    environment:
      - bootstrap.memory_lock=true
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - cluster.routing.allocation.disk.watermark.low=2gb
      - cluster.routing.allocation.disk.watermark.high=1gb
      - cluster.routing.allocation.disk.watermark.flood_stage=512mb
    ports:
      - 9200:9200
    networks:
      - elastic
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

networks:
  elastic: