{"rustc": 5357548097637079788, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 13123396636903684815, "deps": [[1906322745568073236, "pin_project_lite", false, 3600465983184815858], [11033263105862272874, "tracing_core", false, 10337069196399751854]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-303797ddef1f257a/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}