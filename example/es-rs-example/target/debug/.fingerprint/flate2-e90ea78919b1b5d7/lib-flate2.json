{"rustc": 5357548097637079788, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 4186599200243171480, "deps": [[4675849561795547236, "miniz_oxide", false, 17853074662884464245], [5466618496199522463, "crc32fast", false, 573071283128300257]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-e90ea78919b1b5d7/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}