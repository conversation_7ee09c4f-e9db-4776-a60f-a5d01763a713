{"rustc": 5357548097637079788, "features": "[\"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 5347358027863023418, "path": 2743617108624458220, "deps": [[1906322745568073236, "pin_project_lite", false, 3600465983184815858], [3129130049864710036, "memchr", false, 14651041207831788625], [5138218615291878843, "tokio", false, 9297032151420968638], [7620660491849607393, "futures_core", false, 17304776395504049979], [10563170702865159712, "flate2", false, 1097101654473584545]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-3ddabd0ccedd1581/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}