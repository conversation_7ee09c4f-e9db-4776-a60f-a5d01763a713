{"rustc": 5357548097637079788, "features": "[\"default\", \"native-tls\"]", "declared_features": "[\"beta-apis\", \"default\", \"experimental-apis\", \"native-tls\", \"rustls-tls\"]", "target": 16572493702369418937, "profile": 5347358027863023418, "path": 18143232506091497297, "deps": [[40386456601120721, "percent_encoding", false, 3789932629220552641], [2125943931450972187, "base64", false, 4402867284101279030], [2283771217451780507, "serde_with", false, 4444964630545493148], [3150220818285335163, "url", false, 13163856373683809473], [6762477539428817722, "build_script_build", false, 17463868626446288459], [7244058819997729774, "reqwest", false, 12255066000308361222], [9122563107207267705, "dyn_clone", false, 5235652181804034285], [9689903380558560274, "serde", false, 110058752064771001], [15367738274754116744, "serde_json", false, 12986138187582663711], [15908183388125799874, "void", false, 4927816346605106542], [16066129441945555748, "bytes", false, 4906212981623001456], [17917672826516349275, "lazy_static", false, 8838151570027338015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/elasticsearch-f9a3d322a9938a73/dep-lib-elasticsearch", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}