{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 742610365291452999, "deps": [[2924422107542798392, "libc", false, 9292549047950512171], [3080767648169585980, "libc_errno", false, 12864041905323329823], [6166349630582887940, "bitflags", false, 8827898085246839598], [9257091544360225421, "build_script_build", false, 17455141019471945151]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-b457493b2409a86e/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}