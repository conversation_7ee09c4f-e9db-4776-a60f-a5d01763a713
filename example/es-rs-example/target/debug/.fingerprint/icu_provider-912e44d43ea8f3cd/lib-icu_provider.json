{"rustc": 5357548097637079788, "features": "[\"macros\"]", "declared_features": "[\"bench\", \"datagen\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"experimental\", \"log_error_context\", \"logging\", \"macros\", \"serde\", \"std\", \"sync\"]", "target": *******************, "profile": 5347358027863023418, "path": 18065088759513085617, "deps": [[1218499983858120347, "icu_locid", false, 6058635725424742342], [*******************, "stable_deref_trait", false, 3048674019499246664], [5298260564258778412, "displaydoc", false, 13375947167420048169], [8375043423015247637, "tinystr", false, 10516310689636024402], [11734826517668253792, "writeable", false, 9674177089097989458], [14072360194315679348, "zerovec", false, 10119844700901056865], [15837516966853249478, "icu_provider_macros", false, 10493212538251982969], [17046516144589451410, "zerofrom", false, 8306764683590335157], [17505694197072478820, "yoke", false, 12877514691265642134]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/icu_provider-912e44d43ea8f3cd/dep-lib-icu_provider", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}