{"rustc": 5357548097637079788, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 2908210774301854779, "path": 9558663977162717709, "deps": [[15407850927583745935, "adler2", false, 7327801257021510259]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-476d1a336d575001/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}